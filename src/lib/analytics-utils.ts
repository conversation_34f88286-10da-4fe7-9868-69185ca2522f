import { NormalizedTransaction } from '@/types/database';
import { isDateInRange, getTimeRangeDates, dateToLocalDateString } from '@/lib/date-utils';

export interface AnalyticsDataPoint {
  date: string;
  gmv: number;
  aov: number;
  transactionCount: number;
}

export interface PlatformAnalytics {
  platform: string;
  totalGMV: number;
  totalAOV: number;
  totalTransactions: number;
  dataPoints: AnalyticsDataPoint[];
}

/**
 * Calculate GMV (Gross Merchandise Value) from transactions
 * GMV = Sum of all order amounts
 */
export function calculateGMV(transactions: NormalizedTransaction[]): number {
  return transactions.reduce((total, transaction) => {
    return total + (transaction.final_order_amount || transaction.order_amount || 0);
  }, 0);
}

/**
 * Calculate AOV (Average Order Value) from transactions
 * AOV = Total GMV / Number of transactions
 */
export function calculateAOV(transactions: NormalizedTransaction[]): number {
  if (transactions.length === 0) return 0;
  const gmv = calculateGMV(transactions);
  return gmv / transactions.length;
}

/**
 * Group transactions by date and calculate daily metrics
 */
export function groupTransactionsByDate(transactions: NormalizedTransaction[]): AnalyticsDataPoint[] {
  const groupedData: { [date: string]: NormalizedTransaction[] } = {};

  // Group transactions by date
  transactions.forEach(transaction => {
    const date = dateToLocalDateString(new Date(transaction.transaction_date));
    if (!groupedData[date]) {
      groupedData[date] = [];
    }
    groupedData[date].push(transaction);
  });

  // Calculate metrics for each date
  return Object.entries(groupedData)
    .map(([date, dayTransactions]) => ({
      date,
      gmv: calculateGMV(dayTransactions),
      aov: calculateAOV(dayTransactions),
      transactionCount: dayTransactions.length
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}

/**
 * Filter transactions by platform
 */
export function filterTransactionsByPlatform(
  transactions: NormalizedTransaction[],
  platform: string
): NormalizedTransaction[] {
  return transactions.filter(transaction =>
    transaction.platform.toLowerCase() === platform.toLowerCase()
  );
}

/**
 * Filter transactions by date range
 */
export function filterTransactionsByDateRange(
  transactions: NormalizedTransaction[],
  startDate?: string,
  endDate?: string
): NormalizedTransaction[] {
  return transactions.filter(transaction => {
    return isDateInRange(transaction.transaction_date, startDate, endDate);
  });
}

/**
 * Get analytics data for a specific platform
 */
export function getPlatformAnalytics(
  transactions: NormalizedTransaction[],
  platform: string,
  timeRange: string = '30d',
  startDate?: string,
  endDate?: string
): PlatformAnalytics {
  // Filter by platform
  const platformTransactions = filterTransactionsByPlatform(transactions, platform);

  // Filter by date range - custom date range takes precedence over timeRange
  let filteredTransactions = platformTransactions;

  if (startDate || endDate) {
    // Use custom date range
    filteredTransactions = filterTransactionsByDateRange(
      platformTransactions,
      startDate,
      endDate
    );
  } else if (timeRange !== 'all') {
    // Use predefined time range
    const { startDate: calculatedStartDate } = getTimeRangeDates(timeRange);

    filteredTransactions = filterTransactionsByDateRange(
      platformTransactions,
      calculatedStartDate
    );
  }

  // Calculate overall metrics
  const totalGMV = calculateGMV(filteredTransactions);
  const totalAOV = calculateAOV(filteredTransactions);
  const totalTransactions = filteredTransactions.length;

  // Get daily data points
  const dataPoints = groupTransactionsByDate(filteredTransactions);

  return {
    platform,
    totalGMV,
    totalAOV,
    totalTransactions,
    dataPoints
  };
}

/**
 * Format currency values for display
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}

/**
 * Format large numbers with K, M suffixes
 */
export function formatLargeNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}
