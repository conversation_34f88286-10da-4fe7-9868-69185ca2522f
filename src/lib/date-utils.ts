import { format, parseISO } from "date-fns";
import { formatInTimeZone, zonedTimeToUtc, utcToZonedTime } from "date-fns-tz";

/**
 * Get the user's timezone
 */
export function getUserTimezone(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Convert a Date object to YYYY-MM-DD string in local timezone
 * This prevents timezone shift issues when converting dates
 */
export function dateToLocalDateString(date: Date): string {
  const timezone = getUserTimezone();
  return formatInTimeZone(date, timezone, 'yyyy-MM-dd');
}

/**
 * Convert a YYYY-MM-DD string to Date object in local timezone
 * This ensures the date represents the correct day in user's timezone
 */
export function localDateStringToDate(dateString: string): Date {
  const timezone = getUserTimezone();
  // Create date at noon in local timezone to avoid DST issues
  const date = zonedTimeToUtc(new Date(dateString + 'T12:00:00'), timezone);
  return date;
}

/**
 * Safe conversion from YYYY-MM-DD string to Date for date pickers
 * This prevents timezone shift issues when displaying dates in pickers
 */
export function safeStringToDate(dateString: string): Date {
  // Parse the date string as local date (not UTC)
  const [year, month, day] = dateString.split('-').map(Number);
  // Create date at noon to avoid any DST edge cases
  return new Date(year, month - 1, day, 12, 0, 0, 0);
}

/**
 * Get start of day in user's timezone for a given date
 */
export function getStartOfDay(date: Date): Date {
  const timezone = getUserTimezone();
  const dateString = formatInTimeZone(date, timezone, 'yyyy-MM-dd');
  return zonedTimeToUtc(new Date(dateString + 'T00:00:00'), timezone);
}

/**
 * Get end of day in user's timezone for a given date
 */
export function getEndOfDay(date: Date): Date {
  const timezone = getUserTimezone();
  const dateString = formatInTimeZone(date, timezone, 'yyyy-MM-dd');
  return zonedTimeToUtc(new Date(dateString + 'T23:59:59'), timezone);
}

/**
 * Format date for display (handles timezone properly)
 */
export function formatDateForDisplay(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const timezone = getUserTimezone();
  return formatInTimeZone(dateObj, timezone, 'PPP');
}

/**
 * Check if a transaction date falls within a date range (inclusive)
 * Handles timezone properly to avoid off-by-one errors
 */
export function isDateInRange(
  transactionDate: string | Date,
  startDate?: string,
  endDate?: string
): boolean {
  const transactionDateObj = typeof transactionDate === 'string'
    ? parseISO(transactionDate)
    : transactionDate;

  const timezone = getUserTimezone();
  const transactionDateString = formatInTimeZone(transactionDateObj, timezone, 'yyyy-MM-dd');

  if (startDate && transactionDateString < startDate) {
    return false;
  }

  if (endDate && transactionDateString > endDate) {
    return false;
  }

  return true;
}

/**
 * Get date range for predefined time periods (7d, 30d, 90d)
 * Returns dates in YYYY-MM-DD format in user's timezone
 */
export function getTimeRangeDates(timeRange: string): { startDate: string; endDate: string } {
  const timezone = getUserTimezone();
  const now = new Date();
  const endDate = formatInTimeZone(now, timezone, 'yyyy-MM-dd');

  let daysToSubtract: number;
  switch (timeRange) {
    case '7d':
      daysToSubtract = 7;
      break;
    case '30d':
      daysToSubtract = 30;
      break;
    case '90d':
      daysToSubtract = 90;
      break;
    default:
      daysToSubtract = 30;
  }

  const startDateObj = new Date(now);
  startDateObj.setDate(now.getDate() - daysToSubtract);
  const startDate = formatInTimeZone(startDateObj, timezone, 'yyyy-MM-dd');

  return { startDate, endDate };
}

/**
 * Debug function to help troubleshoot date issues
 */
export function debugDate(dateString: string, label: string = "Debug") {
  console.log(`${label}:`, {
    original: dateString,
    timezone: getUserTimezone(),
    safeDate: safeStringToDate(dateString),
    safeDateParts: {
      year: safeStringToDate(dateString).getFullYear(),
      month: safeStringToDate(dateString).getMonth() + 1,
      day: safeStringToDate(dateString).getDate(),
    },
    backToString: dateToLocalDateString(safeStringToDate(dateString)),
    roundTripOK: dateString === dateToLocalDateString(safeStringToDate(dateString))
  });
}
