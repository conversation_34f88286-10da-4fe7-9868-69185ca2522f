/**
 * CSV Mapping Configuration for Shopmy Data
 *
 * This file contains the mapping logic to transform Shopmy CSV format
 * to the normalized_transactions database schema.
 */

import { NormalizedTransaction } from '@/types/database';

// Shopmy CSV column structure based on the provided sample
export interface ShopmyCsvRow {
  Date: string;                    // "06/24/2025"
  Merchant: string;               // "The Home Depot"
  Domain: string;                 // "homedepot.com"
  'Order Amount USD': string;     // "161.99"
  'Commission USD': string;       // "1.33" or "-" for no commission
  'Code Used': string;            // "SHOPSALE" or "-" for no code
  Status: string;                 // "active"
}

// Alternative column names that might appear in CSV files
export const SHOPMY_COLUMN_ALIASES = {
  date: ['Date', 'Transaction Date', 'Order Date', 'date'],
  merchant: ['Merchant', 'Store', 'Retailer', 'merchant'],
  domain: ['Domain', 'Website', 'URL', 'domain'],
  orderAmount: ['Order Amount USD', 'Order Amount', 'Total', 'Amount', 'order_amount'],
  commission: ['Commission USD', 'Commission', 'Earnings', 'commission'],
  codeUsed: ['Code Used', 'Coupon Code', 'Promo Code', 'code_used', 'code'],
  status: ['Status', 'Transaction Status', 'status']
};

// Required columns for successful processing
export const REQUIRED_COLUMNS = ['date', 'merchant', 'orderAmount', 'status'];

// Status mapping from Shopmy to normalized format
export const STATUS_MAPPING: { [key: string]: string } = {
  'active': 'confirmed',
  'pending': 'pending',
  'declined': 'declined',
  'cancelled': 'cancelled',
  'confirmed': 'confirmed',
  'failed': 'declined'
};

/**
 * Normalize column names to match our internal format
 */
export function normalizeColumnName(columnName: string): string | null {
  const normalized = columnName.trim().toLowerCase();

  for (const [key, aliases] of Object.entries(SHOPMY_COLUMN_ALIASES)) {
    if (aliases.some(alias => alias.toLowerCase() === normalized)) {
      return key;
    }
  }

  return null;
}

/**
 * Validate CSV headers and return normalized column mapping
 */
export function validateAndMapHeaders(headers: string[]): {
  isValid: boolean;
  mapping: { [key: string]: number };
  errors: string[];
} {
  const mapping: { [key: string]: number } = {};
  const errors: string[] = [];
  const foundColumns = new Set<string>();

  // Map each header to our normalized format
  headers.forEach((header, index) => {
    const normalizedName = normalizeColumnName(header);
    if (normalizedName) {
      mapping[normalizedName] = index;
      foundColumns.add(normalizedName);
    }
  });

  // Check for required columns
  REQUIRED_COLUMNS.forEach(required => {
    if (!foundColumns.has(required)) {
      errors.push(`Missing required column: ${required}`);
    }
  });

  return {
    isValid: errors.length === 0,
    mapping,
    errors
  };
}

/**
 * Parse date from various formats
 */
export function parseDate(dateString: string): Date | null {
  if (!dateString || dateString.trim() === '' || dateString === '-') {
    return null;
  }

  // Try different date formats

  const cleanDate = dateString.trim();

  // Try MM/DD/YYYY format first (Shopmy default)
  const mmddyyyy = cleanDate.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
  if (mmddyyyy) {
    const [, month, day, year] = mmddyyyy;
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    if (!isNaN(date.getTime())) {
      return date;
    }
  }

  // Try standard Date parsing as fallback
  const date = new Date(cleanDate);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Parse monetary amount from string
 */
export function parseAmount(amountString: string): number {
  if (!amountString || amountString.trim() === '' || amountString === '-') {
    return 0;
  }

  // Remove currency symbols, commas, and whitespace
  const cleanAmount = amountString
    .replace(/[$,\s]/g, '')
    .trim();

  const amount = parseFloat(cleanAmount);
  return isNaN(amount) ? 0 : Math.abs(amount); // Use absolute value to handle negative amounts
}

/**
 * Generate a unique transaction ID
 */
export function generateTransactionId(row: any, rowIndex: number, uploadId: string): string {
  // Create a hash-like ID based on key fields
  const date = row.date || '';
  const merchant = row.merchant || '';
  const amount = row.orderAmount || '';

  // Simple hash function for generating consistent IDs
  const hashInput = `${uploadId}-${date}-${merchant}-${amount}-${rowIndex}`;
  return `shopmy-${hashInput.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()}`;
}

/**
 * Transform a CSV row to normalized transaction format
 */
export function transformCsvRowToTransaction(
  csvRow: any,
  columnMapping: { [key: string]: number }
): Partial<NormalizedTransaction> {
  // Extract values using column mapping
  const getValue = (key: string): string => {
    const columnIndex = columnMapping[key];
    return columnIndex !== undefined ? (csvRow[columnIndex] || '').toString().trim() : '';
  };

  const dateValue = getValue('date');
  const merchantValue = getValue('merchant');
  const domainValue = getValue('domain');
  const orderAmountValue = getValue('orderAmount');
  const commissionValue = getValue('commission');
  const codeUsedValue = getValue('codeUsed');
  const statusValue = getValue('status');

  // Parse and validate data
  const transactionDate = parseDate(dateValue);
  const orderAmount = parseAmount(orderAmountValue);
  const commissionAmount = parseAmount(commissionValue);
  const normalizedStatus = STATUS_MAPPING[statusValue.toLowerCase()] || 'pending';

  if (!transactionDate) {
    throw new Error(`Invalid date format: ${dateValue}`);
  }

  if (!merchantValue) {
    throw new Error('Merchant name is required');
  }

  if (orderAmount <= 0) {
    throw new Error(`Invalid order amount: ${orderAmountValue}`);
  }

  // Generate transaction ID
  const transactionId = `shopmy-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  // Create normalized transaction
  const normalizedTransaction: Partial<NormalizedTransaction> = {
    transaction_id: transactionId,
    platform: 'shopmy',
    source_transaction_id: transactionId, // Use same ID as we generate it
    currency: 'USD', // Shopmy is USD only based on CSV
    order_amount: orderAmount,
    commission_amount: commissionAmount,
    final_order_amount: orderAmount,
    final_commission_amount: commissionAmount,
    order_id: transactionId, // Use transaction ID as order ID
    customer_id: undefined, // Not available in Shopmy CSV
    transaction_date: transactionDate.toISOString(),
    created_date: new Date().toISOString(),
    network_name: 'ShopMy',
    merchant_name: merchantValue,
    merchant_id: domainValue || undefined, // Use domain as merchant ID
    connection_name: domainValue || undefined,
    status: normalizedStatus,
    transaction_type: 'sale',
    decline_reason: normalizedStatus === 'declined' ? 'Unknown' : undefined,
    channel_name: 'affiliate',
    custom_fields: {
      domain: domainValue,
      code_used: codeUsedValue !== '-' ? codeUsedValue : undefined,
      original_status: statusValue
    },
    comments: codeUsedValue !== '-' ? `Promo code used: ${codeUsedValue}` : undefined,
    last_updated: new Date().toISOString()
  };

  return normalizedTransaction;
}

/**
 * Validate a transformed transaction before insertion
 */
export function validateTransaction(transaction: Partial<NormalizedTransaction>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Check required fields
  if (!transaction.transaction_id) errors.push('Transaction ID is required');
  if (!transaction.platform) errors.push('Platform is required');
  if (!transaction.merchant_name) errors.push('Merchant name is required');
  if (!transaction.transaction_date) errors.push('Transaction date is required');
  if (!transaction.order_amount || transaction.order_amount <= 0) {
    errors.push('Valid order amount is required');
  }
  if (!transaction.status) errors.push('Status is required');

  return {
    isValid: errors.length === 0,
    errors
  };
}
