import { useState, useEffect, useCallback } from 'react';
import { PlatformAnalytics } from '@/lib/analytics-utils';

interface UsePlatformAnalyticsReturn {
  data?: PlatformAnalytics;
  loading: boolean;
  error?: string;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for fetching platform analytics data with dynamic time range support
 */
export function usePlatformAnalytics(
  platform: string,
  timeRange: string = 'all'
): UsePlatformAnalyticsReturn {
  const [data, setData] = useState<PlatformAnalytics | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | undefined>(undefined);

  const fetchAnalytics = useCallback(async () => {
    setLoading(true);
    setError(undefined);

    try {
      const response = await fetch(`/api/analytics/platform?platform=${platform}&timeRange=${timeRange}`, {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      setData(result.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch analytics data';
      setError(errorMessage);
      console.error('Error fetching platform analytics:', err);
    } finally {
      setLoading(false);
    }
  }, [platform, timeRange]);

  // Fetch data when platform or timeRange changes
  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  return {
    data,
    loading,
    error,
    refetch: fetchAnalytics,
  };
}
