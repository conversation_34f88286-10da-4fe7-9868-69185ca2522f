"use client";

import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  B<PERSON><PERSON><PERSON>bI<PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"

// Updated imports to point to src/components/dashboard
import { StrackrOverview } from "@/components/dashboard/strackr-overview"
import { ShopMyOverview } from "@/components/dashboard/shopmy-overview"

// Import the hook for analytics data
import { usePlatformAnalytics } from "@/hooks/usePlatformAnalytics";

// BANDAID: Adjust dates to fix timezone discrepancy
const adjustDateForAPI = (dateString: string): string => {
  const date = new Date(dateString);
  date.setDate(date.getDate() + 1);
  return date.toISOString().split('T')[0];
};

export default function HomePage() {
  // State for time ranges - each platform can have its own time range
  const [strackrTimeRange, setStrackrTimeRange] = React.useState("all");
  const [shopMyTimeRange, setShopMyTimeRange] = React.useState("all");

  // State for custom date ranges - each platform can have its own date range
  const [strackrStartDate, setStrackrStartDate] = React.useState<string | undefined>();
  const [strackrEndDate, setStrackrEndDate] = React.useState<string | undefined>();
  const [shopMyStartDate, setShopMyStartDate] = React.useState<string | undefined>();
  const [shopMyEndDate, setShopMyEndDate] = React.useState<string | undefined>();

  // Fetch analytics data for both platforms with dynamic time ranges and date ranges
  // BANDAID: Apply date adjustment for API calls - Strackr doesn't need adjustment, ShopMy does
  const adjustedShopMyStartDate = shopMyStartDate ? adjustDateForAPI(shopMyStartDate) : undefined;
  const adjustedShopMyEndDate = shopMyEndDate ? adjustDateForAPI(shopMyEndDate) : undefined;

  const {
    data: strackrData,
    loading: strackrLoading,
    error: strackrError,
    refetch: refetchStrackr
  } = usePlatformAnalytics('strackr', strackrTimeRange, strackrStartDate, strackrEndDate);

  const {
    data: shopMyData,
    loading: shopMyLoading,
    error: shopMyError,
    refetch: refetchShopMy
  } = usePlatformAnalytics('shopmy', shopMyTimeRange, adjustedShopMyStartDate, adjustedShopMyEndDate);

  // Handle time range changes
  const handleStrackrTimeRangeChange = React.useCallback((timeRange: string) => {
    setStrackrTimeRange(timeRange);
    // Clear custom date range when time range is changed
    if (strackrStartDate || strackrEndDate) {
      setStrackrStartDate(undefined);
      setStrackrEndDate(undefined);
    }
  }, [strackrStartDate, strackrEndDate]);

  const handleShopMyTimeRangeChange = React.useCallback((timeRange: string) => {
    setShopMyTimeRange(timeRange);
    // Clear custom date range when time range is changed
    if (shopMyStartDate || shopMyEndDate) {
      setShopMyStartDate(undefined);
      setShopMyEndDate(undefined);
    }
  }, [shopMyStartDate, shopMyEndDate]);

  // Handlers for custom date range changes
  const handleStrackrDateRangeChange = React.useCallback((startDate?: string, endDate?: string) => {
    setStrackrStartDate(startDate);
    setStrackrEndDate(endDate);
  }, []);

  const handleShopMyDateRangeChange = React.useCallback((startDate?: string, endDate?: string) => {
    setShopMyStartDate(startDate);
    setShopMyEndDate(endDate);
  }, []);

  return (
    <div className="@container/main flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Breadcrumbs and Separator first (or wherever they were originally) */}
      <Breadcrumb className="hidden font-medium md:flex">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="#">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Overview</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Separator className="my-2 hidden md:block" />

      {/* Two-column grid for Strackr and ShopMy analytics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
        {/* Column 1: Strackr Overview */}
        <StrackrOverview
          analyticsData={strackrData}
          loading={strackrLoading}
          error={strackrError}
          timeRange={strackrTimeRange}
          onTimeRangeChange={handleStrackrTimeRangeChange}
          onDateRangeChange={handleStrackrDateRangeChange}
          startDate={strackrStartDate}
          endDate={strackrEndDate}
        />

        {/* Column 2: ShopMy Overview */}
        <ShopMyOverview
          analyticsData={shopMyData}
          loading={shopMyLoading}
          error={shopMyError}
          timeRange={shopMyTimeRange}
          onTimeRangeChange={handleShopMyTimeRangeChange}
          onDateRangeChange={handleShopMyDateRangeChange}
          startDate={shopMyStartDate}
          endDate={shopMyEndDate}
        />
      </div>
    </div>
  );
}
