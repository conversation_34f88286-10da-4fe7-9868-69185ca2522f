'use server';

import { createClient } from '@/supabase/client/server';
import { createClient as createAdminClient } from '@supabase/supabase-js';
import { NormalizedTransaction } from '@/types/database';
import { getPlatformAnalytics, PlatformAnalytics } from '@/lib/analytics-utils';

// Create a Supabase admin client for server-side operations (same as API route)
const supabaseAdmin = createAdminClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// --- Action Types (Define based on actual data needed by components) ---

export type PlatformConnectionStatusData = {
  platform: 'x' | 'linkedin' | 'reddit';
  connection_status: 'connected' | 'disconnected' | 'error' | 'reconnecting' | null;
};

export type RecentTaskData = {
  id: string;
  status: string | null;
  platform: string | null;
  created_at: string;
  // TODO: Add a prompt/description field if available in the schema
  description?: string | null;
};

export type EngagementChartPoint = {
  date: string; // e.g., 'YYYY-MM-DD'
  platform: 'x' | 'linkedin' | 'reddit';
  value: number;
};

// --- Server Actions ---

/**
 * Fetches the connection status for each platform for the current user.
 */
export async function getPlatformConnectionStatuses(): Promise<{ data?: PlatformConnectionStatusData[], error?: string }> {
  try {
    const supabase = await createClient();

    // First try to get the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    console.log('Session check:', { session: !!session, sessionError });

    if (sessionError) {
      console.error('Session error:', sessionError);
      return { error: `Session error: ${sessionError.message}` };
    }

    if (!session) {
      console.error('No session found');
      return { error: 'No active session found' };
    }

    const user = session.user;
    if (!user) {
      console.error('No user in session');
      return { error: 'No user found in session' };
    }

    console.log('User authenticated:', user.id);

    const { data, error } = await supabase
      .from('platform_connections')
      .select('platform, connection_status')
      .eq('user_id', user.id);

    if (error) throw error;

    // Ensure platform is correctly typed if possible
    const typedData = (data || []).map(item => ({
      ...item,
      platform: item.platform as 'x' | 'linkedin' | 'reddit', // Cast platform type
      connection_status: item.connection_status as PlatformConnectionStatusData['connection_status'] // Cast status type
    }));

    return { data: typedData };
  } catch (err: any) {
    console.error('Error fetching platform connection statuses:', err);
    const errorMessage = err instanceof Error ? err.message : 'Failed to fetch connection statuses.';
    return { error: errorMessage };
  }
}

/**
 * Fetches the most recent tasks/sessions for the current user.
 */
export async function getRecentTasks(limit: number = 5): Promise<{ data?: RecentTaskData[], error?: string }> {
  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError) throw new Error(`Authentication error: ${authError.message}`);
    if (!user) throw new Error('User not authenticated');

    // TODO: Add a 'prompt' or 'description' field to the select if it exists
    const { data, error } = await supabase
      .from('browser_use_tasks')
      .select('id, status, platform, created_at') // Add prompt/description here if available
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;

    // TODO: Map 'prompt'/'description' if fetched
    return { data: data || [] };

  } catch (err: any) {
    console.error('Error fetching recent tasks:', err);
    const errorMessage = err instanceof Error ? err.message : 'Failed to fetch recent tasks.';
    return { error: errorMessage };
  }
}

/**
 * Fetches and aggregates engagement data for charts.
 * Placeholder: Currently returns raw data, needs aggregation.
 */
export async function getEngagementChartData(days: number = 30): Promise<{ data?: EngagementChartPoint[], error?: string }> {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError) throw new Error(`Authentication error: ${authError.message}`);
    if (!user) throw new Error('User not authenticated');

    // Fetch raw engagements within the date range
    const { data: rawEngagements, error: engagementsError } = await supabase
      .from('engagements')
      .select('platform, posted_at')
      .eq('user_id', user.id)
      .gte('posted_at', startDate.toISOString()); // Filter by date

    if (engagementsError) {
      console.error("[Action:getEngagementChartData] Supabase Error:", engagementsError);
      return { data: [] }; // Return empty on error
    }

    if (!rawEngagements || rawEngagements.length === 0) {
      console.log("[Action:getEngagementChartData] No engagement data found.");
      return { data: [] };
    }

    // --- Placeholder Aggregation Logic ---
    // TODO: Implement robust aggregation, potentially using a Supabase function (RPC)
    //       or process the raw data here.
    // Example basic aggregation (Group by date and platform):
    const aggregatedData: { [key: string]: EngagementChartPoint } = {};

    for (const engagement of rawEngagements || []) {
        if (!engagement.posted_at || !engagement.platform) continue;
        const dateStr = new Date(engagement.posted_at).toISOString().split('T')[0]; // YYYY-MM-DD
        const platform = engagement.platform as 'x' | 'linkedin' | 'reddit';
        const key = `${dateStr}-${platform}`;

        if (!aggregatedData[key]) {
            aggregatedData[key] = { date: dateStr, platform: platform, value: 0 };
        }
        aggregatedData[key].value += 1;
    }

    const chartData = Object.values(aggregatedData);
    // Sort by date
    chartData.sort((a, b) => a.date.localeCompare(b.date));

    return { data: chartData };

  } catch (error) {
    console.error("[Action:getEngagementChartData] Catch Error:", error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch engagement data.';
    return { error: errorMessage, data: [] };
  }
}

// --- NEW ACTION: Get Mode Activation Progress ---

// Define the subset of profile data needed for mode checks
type ProfileSubsetForModes = {
  industries?: string[] | null;
  interests?: string[] | null;
  description?: string | null;
  target_audience?: string | null;
  problems_solved?: string[] | null;
  relevant_hashtags?: string[] | null;
  competitors?: string[] | null;
  // Assuming upcoming_events_participation exists, though not in migrations 0002, 0007, 0010
  upcoming_events_participation?: any[] | null;
};

export interface ModeActivationResult {
  activeModes: number;
  totalModes: number;
  error?: string;
}

export async function getModeActivationProgress(): Promise<ModeActivationResult> {
  try {
    const supabase = await createClient();

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('Mode Activation: User not authenticated.', userError);
      return { activeModes: 0, totalModes: 7, error: 'User not authenticated' };
    }

    const { data, error: profileError } = await supabase
      .from('profiles')
      .select('industries, interests, description, target_audience, problems_solved, relevant_hashtags, competitors, upcoming_events_participation')
      .eq('id', user.id)
      .single<ProfileSubsetForModes>();

    // Treat 'Row not found' (PGRST116) as an empty profile, not an error
    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error fetching profile for mode activation:', profileError);
      return { activeModes: 0, totalModes: 7, error: 'Failed to fetch profile data.' };
    }

    // Calculate availability even if data is null (profile doesn't exist yet)
    const profileData = data || {};
    const totalModes = 7;
    let activeModes = 0;

    // Check each mode based on profile data
    const needsInterestsOrIndustries = (profileData.interests?.length ?? 0) > 0 || (profileData.industries?.length ?? 0) > 0;
    if (needsInterestsOrIndustries) {
        activeModes += 3; // search_based, timeline_for_you, timeline_following
    }

    if (!!profileData.description && !!profileData.target_audience && (profileData.problems_solved?.length ?? 0) > 0) {
        activeModes += 1; // user_acquisition
    }

    if ((profileData.relevant_hashtags?.length ?? 0) > 0) {
        activeModes += 1; // hashtag_monitoring
    }

    if ((profileData.competitors?.length ?? 0) > 0) {
        activeModes += 1; // competitor_monitoring
    }

    // Check for event_monitoring, safely assuming the column might exist
    if ((profileData.upcoming_events_participation?.length ?? 0) > 0) {
        activeModes += 1; // event_monitoring
    }

    return { activeModes, totalModes };

  } catch (err: any) {
    console.error('Unexpected error fetching mode activation progress:', err);
    const errorMessage = err instanceof Error ? err.message : 'Failed to calculate mode activation progress.';
    return { activeModes: 0, totalModes: 7, error: errorMessage };
  }
}

// --- Analytics Actions for Strackr and ShopMy ---

/**
 * Fetches transaction data for analytics using the same approach as the API route
 */
export async function getTransactionData(
  platform?: string,
  timeRange: string = '30d'
): Promise<{ data?: NormalizedTransaction[], error?: string }> {
  try {
    // Build the query using the same approach as the API route
    // Note: Supabase has a default limit of 1000 rows, so we need to use range() for larger datasets
    let query = supabaseAdmin
      .from('normalized_transactions')
      .select('*')
      .order('transaction_date', { ascending: false });

    // Filter by platform if specified (same as API route)
    if (platform) {
      query = query.eq('platform', platform);
    }

    // Add date filtering only if not "all" time
    if (timeRange !== 'all') {
      const now = new Date();
      const daysToSubtract = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
      const startDate = new Date();
      startDate.setDate(now.getDate() - daysToSubtract);

      query = query.gte('transaction_date', startDate.toISOString().split('T')[0]);
    }

    // Supabase has a hard limit of 1000 rows, so we need to fetch in batches
    // For analytics, we want all data, so we'll fetch multiple pages
    let allData: any[] = [];
    let hasMore = true;
    let offset = 0;
    const batchSize = 1000;

    while (hasMore) {
      const batchQuery = query.range(offset, offset + batchSize - 1);
      const { data: batchData, error } = await batchQuery;

      if (error) {
        console.error('Supabase query error:', error);
        throw new Error(`Failed to fetch transactions: ${error.message}`);
      }

      if (batchData && batchData.length > 0) {
        allData = allData.concat(batchData);
        console.log(`[Analytics] Batch ${Math.floor(offset/batchSize) + 1}: Fetched ${batchData.length} records, total so far: ${allData.length}`);
        offset += batchSize;
        hasMore = batchData.length === batchSize; // Continue if we got a full batch
      } else {
        hasMore = false;
      }

      // Safety check to prevent infinite loops
      if (offset > 10000) {
        console.warn('[Analytics] Safety limit reached, stopping pagination');
        break;
      }
    }

    const data = allData;

    console.log(`[Analytics] Fetched ${data?.length || 0} transactions for platform: ${platform}, timeRange: ${timeRange}`);
    console.log(`[Analytics] Sample data:`, data?.slice(0, 2));

    // Additional debugging for Strackr
    if (platform === 'strackr') {
      console.log(`[Analytics] Strackr Debug: Expected 2000, got ${data?.length || 0} transactions`);
    }

    return { data: data || [] };

  } catch (err: any) {
    console.error('Error fetching transaction data:', err);
    const errorMessage = err instanceof Error ? err.message : 'Failed to fetch transaction data.';
    return { error: errorMessage };
  }
}

/**
 * Fetches analytics data for a specific platform (Strackr or ShopMy)
 */
export async function getPlatformAnalyticsData(
  platform: string,
  timeRange: string = '30d'
): Promise<{ data?: PlatformAnalytics, error?: string }> {
  try {
    const { data: transactions, error } = await getTransactionData(platform, timeRange);

    if (error) {
      return { error };
    }

    if (!transactions || transactions.length === 0) {
      return {
        data: {
          platform,
          totalGMV: 0,
          totalAOV: 0,
          totalTransactions: 0,
          dataPoints: []
        }
      };
    }

    const analytics = getPlatformAnalytics(transactions, platform, timeRange);
    console.log(`[Analytics] Calculated analytics for ${platform}:`, {
      totalGMV: analytics.totalGMV,
      totalAOV: analytics.totalAOV,
      totalTransactions: analytics.totalTransactions,
      dataPointsCount: analytics.dataPoints.length
    });
    return { data: analytics };

  } catch (err: any) {
    console.error('Error fetching platform analytics:', err);
    const errorMessage = err instanceof Error ? err.message : 'Failed to fetch platform analytics.';
    return { error: errorMessage };
  }
}
