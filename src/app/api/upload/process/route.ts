import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/supabase/client/server';
import <PERSON> from 'papaparse';
import { validateAndMapHeaders, transformCsvRowToTransaction, validateTransaction } from '@/lib/csv-mapping';

const BATCH_SIZE = 50; // Process 50 rows at a time

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { uploadId, startRow = 0 } = await request.json();

    if (!uploadId) {
      return NextResponse.json(
        { error: 'Upload ID is required' },
        { status: 400 }
      );
    }

    // Get upload record
    const { data: upload, error: uploadError } = await supabase
      .from('csv_uploads')
      .select('*')
      .eq('id', uploadId)
      .eq('user_id', user.id)
      .single();

    if (uploadError || !upload) {
      return NextResponse.json(
        { error: 'Upload not found' },
        { status: 404 }
      );
    }

    // Download file from storage
    const { data: fileData, error: downloadError } = await supabase.storage
      .from('csv-uploads')
      .download(upload.file_path);

    if (downloadError || !fileData) {
      console.error('Failed to download file:', downloadError);
      return NextResponse.json(
        { error: 'Failed to retrieve file for processing' },
        { status: 500 }
      );
    }

    // Convert blob to text
    const fileContent = await fileData.text();

    // Process the CSV batch
    const result = await processCSVBatch(
      supabase,
      uploadId,
      upload.filename,
      fileContent,
      startRow,
      BATCH_SIZE
    );

    // Calculate totals
    const totalRows = upload.total_rows;
    const newProcessedRows = startRow + result.processedRows;
    const newSuccessfulRows = (upload.successful_rows || 0) + result.successfulRows;
    const newErrorRows = (upload.error_rows || 0) + result.errorRows;
    const isComplete = newProcessedRows >= totalRows;
    const status = isComplete ? 'completed' : 'processing';

    // Update progress
    await supabase
      .from('csv_uploads')
      .update({
        status,
        processed_rows: newProcessedRows,
        successful_rows: newSuccessfulRows,
        error_rows: newErrorRows,
        processing_completed_at: isComplete ? new Date().toISOString() : null,
        updated_at: new Date().toISOString()
      })
      .eq('id', uploadId);

    // Log errors if any
    if (result.errors.length > 0) {
      const errorRecords = result.errors.map(error => ({
        upload_id: uploadId,
        row_number: error.rowNumber,
        error_type: 'PROCESSING_ERROR',
        error_message: error.message,
        raw_row_data: error.rawData
      }));

      await supabase
        .from('csv_upload_errors')
        .insert(errorRecords);
    }

    return NextResponse.json({
      success: true,
      uploadId,
      processedRows: newProcessedRows,
      totalRows,
      successfulRows: newSuccessfulRows,
      errorRows: newErrorRows,
      isComplete,
      nextStartRow: isComplete ? null : newProcessedRows,
      progress: Math.round((newProcessedRows / totalRows) * 100)
    });

  } catch (error) {
    console.error('Processing API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function processCSVBatch(
  supabase: any,
  uploadId: string,
  filename: string,
  fileContent: string,
  startRow: number,
  batchSize: number
): Promise<{
  processedRows: number;
  successfulRows: number;
  errorRows: number;
  errors: any[];
}> {
  try {
    // Parse CSV
    const parseResult = Papa.parse(fileContent, {
      header: false,
      skipEmptyLines: true
    });

    if (parseResult.errors.length > 0) {
      throw new Error(`CSV parsing failed: ${parseResult.errors[0].message}`);
    }

    const rows = parseResult.data as string[][];
    const headers = rows[0];
    const dataRows = rows.slice(1);

    // Validate headers
    const headerValidation = validateAndMapHeaders(headers);
    if (!headerValidation.isValid) {
      throw new Error(`Invalid headers: ${headerValidation.errors.join(', ')}`);
    }

    // Process batch
    const batchRows = dataRows.slice(startRow, startRow + batchSize);
    const transactionsToInsert = [];
    const errors = [];

    for (let i = 0; i < batchRows.length; i++) {
      const rowIndex = startRow + i;
      const row = batchRows[i];

      try {
        const transaction = transformCsvRowToTransaction(
          row,
          headerValidation.mapping,
          uploadId,
          rowIndex,
          filename
        );

        const validation = validateTransaction(transaction);
        if (!validation.isValid) {
          throw new Error(validation.errors.join(', '));
        }

        transactionsToInsert.push(transaction);
      } catch (error) {
        errors.push({
          rowNumber: rowIndex + 2,
          message: error instanceof Error ? error.message : 'Unknown error',
          rawData: row
        });
      }
    }

    // Insert transactions
    if (transactionsToInsert.length > 0) {
      const { error: insertError } = await supabase
        .from('normalized_transactions')
        .insert(transactionsToInsert);

      if (insertError) {
        console.error('Database insert error:', insertError);
        throw new Error(`Database insert failed: ${insertError.message}`);
      }
    }

    return {
      processedRows: batchRows.length,
      successfulRows: transactionsToInsert.length,
      errorRows: errors.length,
      errors
    };

  } catch (error) {
    console.error('CSV batch processing error:', error);
    throw error;
  }
}
