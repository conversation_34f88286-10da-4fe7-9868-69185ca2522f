"use client";

import React from 'react';
import { safeStringToDate, dateToLocalDateString, formatDateForDisplay } from '@/lib/date-utils';

interface DateDebugProps {
  dateString?: string;
  label?: string;
}

export function DateDebug({ dateString, label = "Date Debug" }: DateDebugProps) {
  if (!dateString) return null;

  const convertedDate = safeStringToDate(dateString);
  const backToString = dateToLocalDateString(convertedDate);
  
  return (
    <div className="p-2 bg-gray-100 text-xs font-mono rounded">
      <div className="font-bold">{label}</div>
      <div>Original: {dateString}</div>
      <div>Converted Date: {convertedDate.toString()}</div>
      <div>Date parts: {convertedDate.getFullYear()}-{convertedDate.getMonth() + 1}-{convertedDate.getDate()}</div>
      <div>Back to string: {backToString}</div>
      <div>Display format: {formatDateForDisplay(convertedDate)}</div>
      <div>Round trip OK: {dateString === backToString ? '✅' : '❌'}</div>
    </div>
  );
}
