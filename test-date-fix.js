// Quick test to verify the date conversion fix
// Run this in browser console to test

function safeStringToDate(dateString) {
  const [year, month, day] = dateString.split('-').map(Number);
  return new Date(year, month - 1, day);
}

function dateToLocalDateString(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// Test cases
console.log('Testing date conversion fix:');

// Test today's date
const today = new Date();
const todayString = dateToLocalDateString(today);
console.log('Today:', todayString);

// Test yesterday (23rd)
const yesterday = new Date();
yesterday.setDate(today.getDate() - 1);
const yesterdayString = dateToLocalDateString(yesterday);
console.log('Yesterday:', yesterdayString);

// Test conversion back and forth
const testDate = '2024-06-23';
console.log('Original string:', testDate);

const convertedDate = safeStringToDate(testDate);
console.log('Converted to Date:', convertedDate);
console.log('Date components:', {
  year: convertedDate.getFullYear(),
  month: convertedDate.getMonth() + 1,
  day: convertedDate.getDate()
});

const backToString = dateToLocalDateString(convertedDate);
console.log('Back to string:', backToString);
console.log('Round trip successful:', testDate === backToString);

// Test problematic case
console.log('\nTesting problematic case:');
const june23 = safeStringToDate('2024-06-23');
console.log('June 23rd as Date:', june23);
console.log('June 23rd day:', june23.getDate());
console.log('Should be 23:', june23.getDate() === 23);
